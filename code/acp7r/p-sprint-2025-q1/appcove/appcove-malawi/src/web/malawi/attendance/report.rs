#[approck::http(GET /malawi/attendance/report; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Attendance Records");
        doc.add_css("/malawi/attendance/report.css");
        doc.add_js("/malawi/attendance/report.js");

        doc.add_body(html!(
            panel {
                header {
                    h5 { "Attendance Records" }
                }
                content {
                    table-wrapper.detail-list {
                        table {
                            thead {
                                tr {
                                    th { "Guard Name" }
                                    th { "Shift" }
                                    th { "Date" }
                                    th { "Start Time" }
                                    th { "End Time" }
                                    th { "Present" }
                                    th { "Late Arrival" }
                                    th { "Absent" }
                                }
                            }
                            tbody {
                                tr {
                                    td { "Francis Mponda" }
                                    td { "Night Shift" }
                                    td { "2025-09-02" }
                                    td { "17:00" }
                                    td { "06:00" }
                                    td { "✅" }
                                    td { "Yes" }
                                    td { "❌" }
                                }
                                tr {
                                    td { "Ruth Mass" }
                                    td { "Day Shift" }
                                    td { "2025-09-01" }
                                    td { "06:00" }
                                    td { "17:00" }
                                    td { "✅" }
                                    td { "No" }
                                    td { "❌" }
                                }
                            }    
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}  